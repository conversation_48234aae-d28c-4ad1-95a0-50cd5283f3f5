import { useEffect, useState } from "react";
import {
  DetailsContainer,
  MyTable,
  SimpleAttributeValue,
} from "../../components";
import { <PERSON><PERSON>, Divider, Dropdown, Flex } from "antd";
import { MyTooltip } from "../../components/atoms/MyTooltip";
import { FullscreenOutlined } from "@ant-design/icons";
import {
  GET_COMMENTS,
  NODES_MENU_ITEMS,
  COMMENT_TEMPLATE_ID,
  TRASH_NODES_MENU_ITEMS,
  COMMENT_AUTHOR_ID,
  COMMENT_RATING_ID,
  GET_LOCAL_SETTINGS_KEY,
} from "../../constants";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import {
  DELETED_FLAG,
  ILocalSettings,
  INodeDetails,
  IRelations,
} from "../../interfaces";
import { RootState } from "../../store";
import { useTranslation } from "react-i18next";
import { getAllCommentsDetails, getAllNodeDetails } from "../../services/node";
import { getParentID, transformObjectPath } from "../../utils";
import { toggleColumnWrap } from "../../store/features/columnWrap";
import {
  useHyperlinkActions,
  useNotification,
} from "../../utils/functions/customHooks";
import { useMutation, useQuery, useQueryClient } from "react-query";
import { getAllComments, saveLocalSettings } from "../../services";
import { setHomeSectionMask } from "../../store/features";

const CommentsTable = ({ listeners }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const { handleHyperlinkAction, handleTrashHyperlinkClick } =
    useHyperlinkActions();

  const templatesData = useSelector(
    (root: RootState) => root.templatesStore.templates
  );

  const [rows, setRows] = useState([]);
  const [columns, setColumns] = useState([]);
  const [columnsRequest, setColumnsRequest] = useState([]);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [filters, setFilters] = useState({});
  const [sort, setSort] = useState([]);
  const [pinned, setPinned] = useState([]);
  const [displayedColumns, setDisplayedColumns] = useState([]);
  const [isDetailsOpen, setDetailsOpen] = useState(null);
  const [loading, setLoading] = useState(true);

  const { contextHolder, showSuccessNotification, showErrorNotification } =
    useNotification();

  const homeSectionMask = useSelector(
    (state: RootState) => state.mask.homeSectionMask
  );
  const userInfo = useSelector((root: RootState) => root.auth.userInfo);

  const { data, isLoading, isError } = useQuery(GET_COMMENTS, () =>
    getAllComments(userInfo?.id)
  );

  const localSettingsData = queryClient.getQueryData(
    GET_LOCAL_SETTINGS_KEY
  ) as ILocalSettings;

  const handleNodeClick = async (key, id, name) => {
    switch (key) {
      case "details": {
        setDetailsOpen({ id: id, name: name });
        return;
      }
      case "open-in-new-tab": {
        const baseUrl =
          import.meta.env.VITE_APP_BASE_URL === "/"
            ? ""
            : import.meta.env.VITE_APP_BASE_URL;

        const parentID = await getParentID(id);

        window.open(
          `${window.origin}${baseUrl}/details/${parentID}?nodeId=${id}`
        );
        return;
      }

      case "view-in-trashcan": {
        handleTrashHyperlinkClick(id);
      }
    }
  };

  const generateRowsAndColumns = (
    allComments: INodeDetails[],
    allParents: INodeDetails[]
  ) => {
    const _columns = [];

    const _rows = [];
    const selectedTemplateAttributes =
      templatesData[COMMENT_TEMPLATE_ID]?.attributeTemplates || [];
    selectedTemplateAttributes?.forEach((attribute) => {
      if (attribute?.id !== COMMENT_AUTHOR_ID)
        _columns.push({
          headerName: attribute.name,
          field: attribute.id?.toString(),
          width: attribute?.id === COMMENT_RATING_ID ? 130 : 250,
          cellRenderer: ({ data }) => {
            return attribute?.id === COMMENT_RATING_ID ? (
              <div
                className={`comment-rating ${Object.values(data[attribute.name])[0] === "Negatywna"
                  ? "negative-rating"
                  : "positive-rating"
                  }`}
              />
            ) : (
              <SimpleAttributeValue
                attributeType={attribute.type}
                attributeValue={data[attribute.name] || "-"}
              />
            );
          },
        });
    });

    _columns.push(
      {
        headerName: "Commented in",
        field: "nodeName",
        minWidth: 170,
        flex: 1,
        cellRenderer: (event) => {
          const record = event?.data;
          return (
            <Dropdown
              menu={{
                items: record.inTrash
                  ? TRASH_NODES_MENU_ITEMS
                  : NODES_MENU_ITEMS,
                onClick: (e) =>
                  handleNodeClick(e.key, record.nodeId, record.nodeName),
              }}
              trigger={["contextMenu"]}
            >
              <a
                className="title-container"
                onClick={async (e) => {
                  e.stopPropagation();
                  handleHyperlinkAction({
                    id: record.nodeId,
                    inTrash: record.inTrash,
                  });
                }}
              >
                <p className={` ${record.inTrash ? "trash-hyperlink" : ""}`}>
                  {record.nodeName}
                </p>
              </a>
            </Dropdown>
          );
        },
      },

      {
        headerName: "Path",
        field: "nodePath",
        minWidth: 240,
        flex: 1,
        cellRenderer: ({ data }) => (
          <p className="right-align">
            {data?.nodePath
              ? transformObjectPath(data?.nodePath, data.inTrash)
              : "-"}
          </p>
        ),
      }
    );

    setColumns(_columns);

    allComments?.forEach(async (node: INodeDetails) => {
      const row = {};
      row["id"] = node.id;
      node?.body?.forEach((attribute) => {
        if (attribute?.id !== COMMENT_AUTHOR_ID)
          row[attribute?.name] = attribute?.value || "-";
      });
      const parentNode = allParents?.find(
        (parent) => parent.id === node?.parentId
      );

      row["nodeName"] = parentNode?.name;
      row["nodeId"] = parentNode?.id;
      row["inTrash"] = parentNode?.flag?.includes(DELETED_FLAG);
      row["nodePath"] = parentNode?.pathName;

      _rows.push(row);
    });

    setRows(_rows);
    setLoading(false);
  };

  const getParentDetails = async () => {
    const parentIds = [];
    const nodeIds = [];
    data?.forEach((node: IRelations) => {
      if (!nodeIds.includes(node.nodeId)) {
        nodeIds.push(node.nodeId);
      }
    });

    const allCommentsDetails = await getAllCommentsDetails(nodeIds);

    allCommentsDetails?.forEach((node) => {
      if (!parentIds.includes(node?.parentId)) {
        parentIds.push(node.parentId);
      }
    });

    const allParentDetails = await getAllNodeDetails(parentIds?.join());

    generateRowsAndColumns(allCommentsDetails, allParentDetails);
  };

  useEffect(() => {
    if (!data) {
      return;
    }

    getParentDetails();
  }, [data]);

  useEffect(() => {
    if (!localSettingsData) {
      return;
    }

    if (
      localSettingsData &&
      columns?.length > 0 &&
      localSettingsData?.body[0]?.value?.myCommentsTable &&
      localSettingsData?.body[0]?.value?.myCommentsTable?.columns.length > 0
    ) {
      if (localSettingsData?.body[0]?.value?.myCommentsTable?.columns) {
        const pinned =
          localSettingsData?.body[0]?.value?.myCommentsTable?.pinned || [];
        const sort =
          localSettingsData?.body[0]?.value?.myCommentsTable?.sort || [];

        const allColumns = [];
        localSettingsData.body[0].value.myCommentsTable.columns?.forEach(
          (column) => {
            const index = columns.findIndex((item) => item.field === column);
            allColumns.push({
              ...columns[index],
              pinned: pinned?.includes(column) ? "left" : null,
              sort: sort?.find((val) => val.colId === column)?.sort || null,
            });
          }
        );
        setColumnsRequest(
          localSettingsData?.body[0]?.value?.myCommentsTable?.columns
        );
        setPinned(localSettingsData?.body[0]?.value?.myCommentsTable?.pinned);
        setSort(localSettingsData?.body[0]?.value?.myCommentsTable?.sort);
        setFilters(localSettingsData?.body[0]?.value?.myCommentsTable?.filters);
        setDisplayedColumns(allColumns);
      }
    } else {
      setDisplayedColumns(columns);
      setColumnsRequest(columns?.map((col) => col.field));
    }
  }, [localSettingsData, resetTrigger, columns]);

  const detectChange = () => {
    dispatch(setHomeSectionMask("comments"));
  };

  const mutation = useMutation(saveLocalSettings, {
    onSuccess: () => {
      showSuccessNotification("Changes published successfully!");
      queryClient.invalidateQueries(GET_LOCAL_SETTINGS_KEY);
      dispatch(setHomeSectionMask(null));
    },
    onError: () => {
      showErrorNotification("Unable to save data!");
      dispatch(setHomeSectionMask(null));
    },
  });

  const handleSave = () => {
    const request = {
      value: {
        ...(localSettingsData?.body
          ? localSettingsData?.body[0]?.value || {}
          : {}),
        myCommentsTable: {
          columns: columnsRequest,
          filters: filters,
          sort: sort,
          pinned: pinned,
        },
      },
    };
    mutation.mutate(request);
  };

  const handleCancel = () => {
    setResetTrigger((trigger) => trigger + 1);
    setTimeout(() => {
      dispatch(setHomeSectionMask(null));
    }, 200);
  };

  return (
    <>
      {contextHolder}
      <Flex className="header-wrapper" justify="space-between" align="center">
        <h4 {...listeners}>{t("Comments")}</h4>
        {!homeSectionMask && (
          <div className="actions">
            <Link to="/comments" className="view-all">
              <MyTooltip title="View all">
                <FullscreenOutlined />
              </MyTooltip>
            </Link>
          </div>
        )}

        {homeSectionMask === "comments" && (
          <Flex gap={10}>
            <Button
              className="breadcrumb-button cancel cancel-button"
              onClick={handleCancel}
            >
              {t("Cancel")}
            </Button>
            <Button
              className="breadcrumb-button save-button"
              onClick={handleSave}
              loading={mutation.isLoading}
            >
              {t("Save")}
            </Button>
          </Flex>
        )}
      </Flex>
      <Divider />

      {/* DEBUG: Test wrap toggle */}
      <Button
        onClick={() => {
          console.log('Test button clicked');
          dispatch(toggleColumnWrap({ tableId: "comments-table", columnId: "nodePath" }));
        }}
        style={{ marginBottom: 10 }}
      >
        Test Wrap Toggle for Path Column
      </Button>

      <article>
        <MyTable
          loading={loading || isLoading}
          height={"260px"}
          isError={isError}
          data={rows}
          columns={displayedColumns}
          emptyMessage="No comments"
          excelFileName="mycomments"
          detectChange={detectChange}
          resetTrigger={resetTrigger}
          setPinned={setPinned}
          setColumnsRequest={setColumnsRequest}
          setFilters={setFilters}
          setSort={setSort}
          tableId="comments-table"
          initialFilters={
            localSettingsData?.body[0]?.value?.myCommentsTable?.filters || {}
          }
        />
      </article>

      {!!isDetailsOpen && (
        <DetailsContainer
          id={isDetailsOpen.id}
          isOpen={!!isDetailsOpen}
          onClose={() => setDetailsOpen(null)}
          title={isDetailsOpen.name}
        />
      )}
    </>
  );
};

export { CommentsTable };
