import {
  DeleteOutlined,
  PushpinFilled,
  PushpinOutlined,
} from "@ant-design/icons";
import { withErrorBoundary } from "../../withErrorBoundary";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { toggleColumnWrap } from "../../../store/features/columnWrap";
import { RootState } from "../../../store";

const CustomHeaderBase = (props) => {
  const dispatch = useDispatch();
  const columnWrapStates = useSelector((state: RootState) => state.columnWrap.columnWrapStates);

  // Generate unique table ID and column ID for wrap state management
  console.log('CustomHeader context:', props.context);
  const tableId = props.context?.tableId || 'default-table';
  const columnId = props.column.getColId();
  const wrapStateKey = `${tableId}-${columnId}`;
  const isWrapped = columnWrapStates[wrapStateKey] || false;
  console.log('CustomHeader tableId:', tableId, 'columnId:', columnId);

  const pinLeft = () => {
    props.api.applyColumnState({
      state: [{ colId: props.column.getColId(), pinned: "left" }],
    });
  };

  const unpin = () => {
    props.api.applyColumnState({
      state: [{ colId: props.column.getColId(), pinned: null }],
    });
  };

  const toggleFilter = (event) => {
    props.showFilter(event.target);
  };

  const handleSortIconClick = (sortOption) => {
    props.api.applyColumnState({
      state: [{ colId: props.column.getColId(), sort: sortOption }],
      defaultState: { sort: null },
    });
    props.api.refreshHeader();
  };

  const handleWrapToggle = () => {
    console.log('Wrap toggle clicked for:', { tableId, columnId, currentState: isWrapped });
    dispatch(toggleColumnWrap({ tableId, columnId }));
    console.log('Redux action dispatched');
    // Let the MyTable useEffect handle the refresh when state updates
  };

  const currentSort = props.column.getSort();

  return (
    <div
      className="custom-header-container"
      onClick={(e) => e.stopPropagation()}
    >
      <p>{props.displayName}</p>
      <div className="column-actions" style={{ color: "var(--color-text)" }}>
        {props?.column?.colDef?.withDelete && (
          <DeleteOutlined
            onClick={props.onColumnsDelete}
            style={{ color: "red", marginRight: 2 }}
          />
        )}

        {/* Wrap/No-wrap toggle icon - placed before other icons */}
        <span title={isWrapped ? "Disable text wrapping" : "Enable text wrapping"}>
          <svg
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            onClick={handleWrapToggle}
            style={{
              cursor: "pointer",
              color: isWrapped ? "red" : "var(--color-text)",
              marginRight: "2px",
            }}
          >
            {isWrapped ? (
              // No-wrap icon (straight lines)
              <>
                <path d="M5.63,10.3h4.7a.38.38,0,1,0,0-.75H5.63a.38.38,0,0,0,0,.75Z" fill="currentColor" />
                <path d="M14.37,9.55H13a.38.38,0,0,0,0,.75h1.37a.38.38,0,0,0,0-.75Z" fill="currentColor" />
                <path d="M9.67,7.64h4.7a.38.38,0,0,0,.37-.38.37.37,0,0,0-.37-.37H9.67a.37.37,0,0,0-.38.37A.38.38,0,0,0,9.67,7.64Z" fill="currentColor" />
                <path d="M5.63,7.64H7a.38.38,0,0,0,.37-.38A.37.37,0,0,0,7,6.89H5.63a.37.37,0,0,0-.37.37A.38.38,0,0,0,5.63,7.64Z" fill="currentColor" />
                <path d="M14.37,12.21H9.67a.37.37,0,0,0-.38.37.38.38,0,0,0,.38.38h4.7a.38.38,0,0,0,.37-.38A.37.37,0,0,0,14.37,12.21Z" fill="currentColor" />
                <path d="M7,12.21H5.63a.37.37,0,0,0-.37.37.38.38,0,0,0,.37.38H7a.38.38,0,0,0,.37-.38A.37.37,0,0,0,7,12.21Z" fill="currentColor" />
              </>
            ) : (
              // Wrap icon (curved lines with arrow)
              <>
                <path d="M2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5H21.25C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5H2.75Z" fill="currentColor" />
                <path d="M2.75 11.5C2.33579 11.5 2 11.8358 2 12.25C2 12.6642 2.33579 13 2.75 13H19C20.3807 13 21.5 14.1193 21.5 15.5C21.5 16.8807 20.3807 18 19 18H14.5607L15.2803 17.2803C15.5732 16.9874 15.5732 16.5126 15.2803 16.2197C14.9874 15.9268 14.5126 15.9268 14.2197 16.2197L12.2197 18.2197C11.9268 18.5126 11.9268 18.9874 12.2197 19.2803L14.2197 21.2803C14.5126 21.5732 14.9874 21.5732 15.2803 21.2803C15.5732 20.9874 15.5732 20.5126 15.2803 20.2197L14.5607 19.5H19C21.2091 19.5 23 17.7091 23 15.5C23 13.2909 21.2091 11.5 19 11.5H2.75Z" fill="currentColor" />
                <path d="M2 18.75C2 18.3358 2.33579 18 2.75 18H9.25C9.66421 18 10 18.3358 10 18.75C10 19.1642 9.66421 19.5 9.25 19.5H2.75C2.33579 19.5 2 19.1642 2 18.75Z" fill="currentColor" />
              </>
            )}
          </svg>
        </span>

        {props?.column?.pinned ? (
          <PushpinFilled onClick={unpin} style={{ color: "red" }} />
        ) : (
          <PushpinOutlined onClick={pinLeft} />
        )}
        {currentSort === "asc" ? (
          <i
            className="pi pi-sort-amount-up-alt"
            title="Sort Ascending"
            onClick={() => handleSortIconClick("desc")}
            style={{ color: "red" }}
          />
        ) : currentSort === "desc" ? (
          <i
            className="pi pi-sort-amount-down-alt"
            title="Sort Ascending"
            onClick={() => handleSortIconClick(null)}
            style={{ color: "red" }}
          />
        ) : (
          <i
            className="pi pi-sort-alt"
            title="Sort"
            onClick={() => handleSortIconClick("asc")}
          ></i>
        )}
        <i
          className={`pi pi-filter`}
          title="Filter"
          onClick={toggleFilter}
          style={{
            cursor: "pointer",
            color: props.column.filterActive ? "red" : "var(--color-text)",
          }}
        ></i>
      </div>
    </div>
  );
};

export const CustomHeader = withErrorBoundary(
  React.memo(CustomHeaderBase),
  "error.generic"
);
