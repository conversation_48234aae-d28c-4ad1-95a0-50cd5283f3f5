.ag-header-container {
  background-color: var(--color-light);
  color: var(--color-text);
  font-size: 13px;
  font-weight: 700;
}

.ag-header-viewport {
  background-color: var(--color-light);
}

.ag-pinned-left-header {
  background-color: var(--color-light);
  color: var(--color-text);
}

.ag-body .title-container {
  cursor: pointer;
  color: var(--color-text);
}

.ag-body .ant-dropdown-trigger:hover {
  text-decoration: underline;
}

.custom-header-container {
  display: flex;
  width: 100%;
}

.custom-header-container>p {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  color: gray;
  font-weight: 500;
}

.ag-cell {
  font-size: 12px;
}

.column-actions {
  display: flex;
  margin-left: auto;
  font-size: 14px;
  gap: 3px;
  align-items: center;
}

.column-actions i,
.column-actions span {
  cursor: pointer;
}

.ag-cell img {
  object-fit: contain;
}

/* Virtualization optimizations for all tables */
.ag-center-cols-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-viewport {
  overflow-y: auto !important;
  scrollbar-width: thin;
}

.ag-body-horizontal-scroll {
  height: auto !important;
}

.ag-root-wrapper {
  overflow: hidden;
}

/* Ensure proper height calculation */
.ag-layout-normal {
  height: 100% !important;
}

/* Text wrapping override classes - highest precedence */
.ag-cell.column-wrapped,
.ag-cell.column-wrapped *,
.ag-cell.column-wrapped p,
.ag-cell.column-wrapped span,
.ag-cell.column-wrapped div {
  white-space: normal !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}

/* Ensure wrapped columns have proper height - more specific targeting */
.ag-row:has(.column-wrapped) {
  height: auto !important;
  min-height: 38px !important;
}

.ag-row:has(.column-wrapped) .ag-cell {
  height: auto !important;
  min-height: 38px !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  display: flex !important;
  align-items: flex-start !important;
}

/* Fallback for browsers that don't support :has() */
.ag-row.has-wrapped-columns {
  height: auto !important;
  min-height: 38px !important;
}

.ag-row.has-wrapped-columns .ag-cell {
  height: auto !important;
  min-height: 38px !important;
  padding-top: 8px !important;
  padding-bottom: 8px !important;
  display: flex !important;
  align-items: flex-start !important;
}

/* Specific override for transformObjectPath components */
.ag-cell.column-wrapped .path-separator-color {
  white-space: normal !important;
}

/* Override for any nested flex containers in wrapped cells */
.ag-cell.column-wrapped [style*="white-space: nowrap"] {
  white-space: normal !important;
}