import { createSlice } from "@reduxjs/toolkit";

export interface ColumnWrapState {
  // Key: tableId-columnId, Value: boolean (true = wrapped, false = nowrap)
  columnWrapStates: Record<string, boolean>;
}

const initialState: ColumnWrapState = {
  columnWrapStates: {},
};

export const columnWrapSlice = createSlice({
  name: "columnWrap",
  initialState,
  reducers: {
    toggleColumnWrap: (state, action) => {
      const { tableId, columnId } = action.payload;
      const key = `${tableId}-${columnId}`;
      state.columnWrapStates[key] = !state.columnWrapStates[key];
    },
    
    setColumnWrap: (state, action) => {
      const { tableId, columnId, isWrapped } = action.payload;
      const key = `${tableId}-${columnId}`;
      state.columnWrapStates[key] = isWrapped;
    },
    
    setMultipleColumnWrap: (state, action) => {
      const { tableId, columnStates } = action.payload;
      columnStates.forEach(({ columnId, isWrapped }) => {
        const key = `${tableId}-${columnId}`;
        state.columnWrapStates[key] = isWrapped;
      });
    },
    
    clearTableColumnWrap: (state, action) => {
      const { tableId } = action.payload;
      Object.keys(state.columnWrapStates).forEach(key => {
        if (key.startsWith(`${tableId}-`)) {
          delete state.columnWrapStates[key];
        }
      });
    },
    
    resetColumnWrap: (state) => {
      state.columnWrapStates = {};
    },
  },
});

export const {
  toggleColumnWrap,
  setColumnWrap,
  setMultipleColumnWrap,
  clearTableColumnWrap,
  resetColumnWrap,
} = columnWrapSlice.actions;

export default columnWrapSlice.reducer;
